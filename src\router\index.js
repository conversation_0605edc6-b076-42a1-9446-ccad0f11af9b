import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/home',
    name: 'Home',
    component: () => import('../views/home/<USER>'),
    meta: {
      requiresAuth: false,
      title: '首页'
    }
  },
  {
    path: '/assistantdecision',
    name: 'assistantdecision',
    component: () => import('../views/home/<USER>'),
    meta: {
      requiresAuth: false,
      title: '辅助决策'
    }
  },
  {
    path: '/intelligence',
    name: 'intelligence',
    component: () => import('../views/home/<USER>'),
    meta: {
      requiresAuth: false,
      title: '智能感知'
    }
  },
  {
    path: '/callPolice',
    name: 'callPolice',
    component: () => import('../views/home/<USER>'),
    meta: {
      requiresAuth: false,
      title: '报警演案'
    }
  },
  {
    path: '/callPhone',
    name: 'callPhone',
    component: () => import('../components/CallPhone.vue'),
    meta: {
      requiresAuth: false,
      title: '拨打电话'
    }
  },
  {
    path: '/test-flood-report',
    name: 'TestFloodReport',
    component: () => import('../views/test/FloodReportTest.vue'),
    meta: {
      requiresAuth: false,
      title: '防汛简报测试'
    }
  },
  {
    path: '/',
    name: '内涝安全预警监测辅助决策系统',
    component: () => import('../views/home/<USER>'),
    meta: {
      requiresAuth: false,
      title: '内涝安全预警监测辅助决策系统'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || import.meta.env.VITE_APP_TITLE || '太原智慧城市'

  // 直接放行所有页面
  next()
})

export default router
